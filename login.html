<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المراسلات</title>
    <link href="css/bootstrap-arabic.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-lg-4 col-md-6 col-sm-8">
                <div class="card shadow-lg border-0 fade-in">
                    <div class="card-header bg-gradient-primary text-white text-center py-4 border-0">
                        <div class="mb-3">
                            <i class="fas fa-user-shield fa-3x"></i>
                        </div>
                        <h3 class="fw-bold mb-0">تسجيل الدخول</h3>
                        <p class="mb-0 opacity-75">نظام إدارة المراسلات الإدارية</p>
                    </div>
                    <div class="card-body p-4">
                        <!-- System Status -->
                        <div id="systemStatus" class="alert alert-success border-0 text-center mb-4">
                            <i class="fas fa-check-circle me-2"></i>
                            <span id="statusMessage">النظام جاهز للاستخدام</span>
                        </div>

                        <!-- Login Form -->
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label fw-bold">
                                    <i class="fas fa-user me-2"></i>
                                    اسم المستخدم
                                </label>
                                <input type="text" class="form-control"
                                       id="username" name="username" required
                                       placeholder="أدخل اسم المستخدم">
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label fw-bold">
                                    <i class="fas fa-lock me-2"></i>
                                    كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control"
                                           id="password" name="password" required
                                           placeholder="أدخل كلمة المرور">
                                    <button class="btn btn-outline-secondary" type="button"
                                            onclick="togglePasswordVisibility()">
                                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    تذكرني
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </button>
                        </form>

                        <hr class="my-3">

                        <!-- Default Users Info -->
                        <div class="text-center">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-users me-2"></i>
                                المستخدمين الافتراضيين
                            </h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="card bg-light border-0 demo-card" style="cursor: pointer;">
                                        <div class="card-body p-3">
                                            <div class="text-primary mb-2">
                                                <i class="fas fa-user-shield fa-lg"></i>
                                            </div>
                                            <h6 class="card-title mb-1">مدير النظام</h6>
                                            <small class="text-muted">
                                                <strong>المستخدم:</strong> admin<br>
                                                <strong>كلمة المرور:</strong> admin123
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light border-0 demo-card" style="cursor: pointer;">
                                        <div class="card-body p-3">
                                            <div class="text-success mb-2">
                                                <i class="fas fa-user fa-lg"></i>
                                            </div>
                                            <h6 class="card-title mb-1">موظف</h6>
                                            <small class="text-muted">
                                                <strong>المستخدم:</strong> employee1<br>
                                                <strong>كلمة المرور:</strong> emp123
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-3">

                        <!-- Back to Activation -->
                        <div class="text-center">
                            <a href="index.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة لصفحة التفعيل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Application Scripts -->
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>

    <script>
        // Check system access and update status
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize activation system
            if (typeof activationSystem === 'undefined') {
                if (typeof ActivationSystem !== 'undefined') {
                    activationSystem = new ActivationSystem();
                }
            }

            // Check if user can access the system
            if (activationSystem && !activationSystem.canAccessSystem()) {
                window.location.href = 'index.html';
                return;
            }

            // Update system status display
            updateSystemStatus();

            // Auto-fill demo credentials on demo button click
            setupDemoLogin();
        });

        function updateSystemStatus() {
            const statusElement = document.getElementById('systemStatus');
            const messageElement = document.getElementById('statusMessage');

            if (activationSystem && statusElement && messageElement) {
                const status = activationSystem.getSystemStatus();

                statusElement.className = `alert alert-${status.class} border-0 text-center mb-4`;
                messageElement.textContent = status.message;
            }
        }

        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        function setupDemoLogin() {
            // Add click handlers for demo login cards
            const demoCards = document.querySelectorAll('.demo-card');

            demoCards.forEach((card, index) => {
                card.addEventListener('click', function() {
                    if (index === 0) {
                        // Admin card
                        document.getElementById('username').value = 'admin';
                        document.getElementById('password').value = 'admin123';
                        this.classList.add('border-primary');
                        setTimeout(() => this.classList.remove('border-primary'), 1000);
                    } else {
                        // Employee card
                        document.getElementById('username').value = 'employee1';
                        document.getElementById('password').value = 'emp123';
                        this.classList.add('border-success');
                        setTimeout(() => this.classList.remove('border-success'), 1000);
                    }
                });

                // Add hover effect
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
        }

        // Handle Enter key in form fields
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && (e.target.id === 'username' || e.target.id === 'password')) {
                e.preventDefault();
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
