/* ANTI-FLICKER CSS FOR DESKTOP APPS */

/* منع الوميض عند التحميل */
html {
    opacity: 1;
    transition: opacity 0.1s ease-in;
}

html.loaded {
    opacity: 1;
}

/* ABSOLUTE STATIC CSS - ZERO MOVEMENT */

/* Complete Browser Reset */
* {
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    transform: none !important;
    transition: none !important;
    animation: none !important;
    -webkit-transform: none !important;
    -moz-transform: none !important;
    -ms-transform: none !important;
    -o-transform: none !important;
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -ms-transition: none !important;
    -o-transition: none !important;
    -webkit-animation: none !important;
    -moz-animation: none !important;
    -ms-animation: none !important;
    -o-animation: none !important;
    will-change: auto !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    -moz-backface-visibility: hidden !important;
    perspective: none !important;
    -webkit-perspective: none !important;
    -moz-perspective: none !important;
}

/* Absolute Fixed Page */
html {
    width: 100vw !important;
    height: 100vh !important;
    overflow: hidden !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

body {
    width: 100vw !important;
    height: 100vh !important;
    overflow: hidden !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: Arial, sans-serif !important;
    direction: rtl !important;
    background: #1a365d !important;
    color: #333 !important;
}

/* Absolute Static Container */
.main-container {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
    transform: none !important;
    -webkit-transform: none !important;
    transition: none !important;
    -webkit-transition: none !important;
    animation: none !important;
    -webkit-animation: none !important;
}

/* Static Activation Box */
.activation-box {
    background: white !important;
    border-radius: 10px !important;
    width: 100% !important;
    max-width: 400px !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
    position: relative !important;
    transform: none !important;
    -webkit-transform: none !important;
    transition: none !important;
    -webkit-transition: none !important;
    animation: none !important;
    -webkit-animation: none !important;
}

/* Header */
.header-section {
    background: #1a365d;
    color: white;
    padding: 30px 20px;
    text-align: center;
    border-radius: 10px 10px 0 0;
}

.logo-icon {
    font-size: 40px;
    margin-bottom: 10px;
}

.system-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
}

.system-subtitle {
    font-size: 14px;
    opacity: 0.9;
}

/* Status */
.status-section {
    padding: 20px;
}

.status-box {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    min-height: 50px;
}

.status-box.info { background: #3b82f6; color: white; }
.status-box.success { background: #22c55e; color: white; }
.status-box.warning { background: #f59e0b; color: white; }
.status-box.error { background: #ef4444; color: white; }

.status-icon {
    font-size: 20px;
    margin-left: 10px;
}

.status-text {
    font-size: 14px;
    font-weight: bold;
}

/* Form */
.form-section {
    padding: 0 20px 20px;
}

.input-group {
    margin-bottom: 15px;
}

.input-label {
    display: block;
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.text-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    text-align: center;
    background: white;
    color: #333;
    outline: none;
}

.text-input:focus {
    border-color: #1a365d;
}

.input-help {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 5px;
}

/* Buttons */
.action-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50px;
}

.action-btn.primary { background: #1a365d; color: white; }
.action-btn.success { background: #22c55e; color: white; }
.action-btn.warning { background: #f59e0b; color: white; }
.action-btn.error { background: #ef4444; color: white; }

/* Footer */
.footer-section {
    padding: 20px;
    background: #f5f5f5;
    border-radius: 0 0 10px 10px;
    text-align: center;
}

.system-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.security-info {
    font-size: 11px;
    color: #22c55e;
    margin-bottom: 5px;
}

.dev-info {
    font-size: 10px;
    color: #f59e0b;
    background: #fff3cd;
    padding: 5px 10px;
    border-radius: 5px;
    margin-top: 5px;
    display: inline-block;
}

/* BROWSER SPECIFIC FIXES */

/* Disable smooth scrolling */
html {
    scroll-behavior: auto !important;
    -webkit-scroll-behavior: auto !important;
    -moz-scroll-behavior: auto !important;
    -ms-scroll-behavior: auto !important;
}

/* Disable all browser animations */
*, *::before, *::after {
    -webkit-animation-duration: 0s !important;
    -webkit-animation-delay: 0s !important;
    -webkit-transition-duration: 0s !important;
    -webkit-transition-delay: 0s !important;
    -moz-animation-duration: 0s !important;
    -moz-animation-delay: 0s !important;
    -moz-transition-duration: 0s !important;
    -moz-transition-delay: 0s !important;
    -ms-animation-duration: 0s !important;
    -ms-animation-delay: 0s !important;
    -ms-transition-duration: 0s !important;
    -ms-transition-delay: 0s !important;
    -o-animation-duration: 0s !important;
    -o-animation-delay: 0s !important;
    -o-transition-duration: 0s !important;
    -o-transition-delay: 0s !important;
}

/* Disable hardware acceleration */
* {
    -webkit-transform: translateZ(0) !important;
    -moz-transform: translateZ(0) !important;
    -ms-transform: translateZ(0) !important;
    -o-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-transform-style: flat !important;
    -moz-transform-style: flat !important;
    -ms-transform-style: flat !important;
    -o-transform-style: flat !important;
    transform-style: flat !important;
}

/* Force static rendering */
body, html {
    -webkit-font-smoothing: none !important;
    -moz-osx-font-smoothing: unset !important;
    text-rendering: optimizeSpeed !important;
    image-rendering: pixelated !important;
    -ms-interpolation-mode: nearest-neighbor !important;
}

/* Disable all user interactions that might cause movement */
* {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
    pointer-events: auto !important;
}

/* Allow only specific interactions */
.text-input, .action-btn {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    pointer-events: auto !important;
}

/* END OF ABSOLUTE STATIC CSS */
