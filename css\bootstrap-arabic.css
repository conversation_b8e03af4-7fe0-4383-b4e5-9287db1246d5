/* Bootstrap Arabic Theme - نظام إدارة المراسلات */

/* Import Bootstrap RTL */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Arabic Font Configuration */
:root {
    --bs-font-sans-serif: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
    --bs-body-font-family: var(--bs-font-sans-serif);
    --bs-body-font-size: 1rem;
    --bs-body-font-weight: 400;
    --bs-body-line-height: 1.6;
    --bs-body-color: #212529;
    --bs-body-bg: #f8f9fa;
    
    /* Custom Colors */
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0284c7;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #10b981 100%);
    --gradient-warning: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Global Styles */
body {
    font-family: var(--bs-font-sans-serif);
    direction: rtl;
    background: var(--bs-body-bg);
}

/* Custom Bootstrap Overrides */
.btn {
    font-weight: 500;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.card {
    border: none;
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.form-control, .form-select {
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
    min-height: 100vh;
    box-shadow: var(--shadow-lg);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 1rem 1.5rem;
    margin: 0.25rem 1rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-left: 0.75rem;
}

/* Header Styles */
.header-gradient {
    background: var(--gradient-primary);
    color: white;
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border-right: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.stats-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.stats-card.success {
    border-right-color: var(--success-color);
}

.stats-card.warning {
    border-right-color: var(--warning-color);
}

.stats-card.info {
    border-right-color: var(--info-color);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Table Styles */
.table-modern {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-modern thead {
    background: var(--gradient-primary);
    color: white;
}

.table-modern th {
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.table-modern td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.table-modern tbody tr:hover {
    background: #f8fafc;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 1rem 1rem 0 0;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.75rem;
    border-right: 4px solid;
}

.alert-primary {
    background: rgba(30, 64, 175, 0.1);
    border-right-color: var(--primary-color);
    color: var(--primary-dark);
}

.alert-success {
    background: rgba(5, 150, 105, 0.1);
    border-right-color: var(--success-color);
    color: #065f46;
}

.alert-warning {
    background: rgba(217, 119, 6, 0.1);
    border-right-color: var(--warning-color);
    color: #92400e;
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    border-right-color: var(--danger-color);
    color: #991b1b;
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        position: fixed;
        top: 0;
        right: 0;
        width: 280px;
        z-index: 1050;
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 1rem;
    }
}

/* Loading Spinner */
.spinner-custom {
    width: 2rem;
    height: 2rem;
    border: 3px solid rgba(30, 64, 175, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Utilities */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-warning {
    background: var(--gradient-warning);
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}
