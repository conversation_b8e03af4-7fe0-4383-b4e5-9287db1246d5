const http = require('http');
const fs = require('fs');
const path = require('path');

const port = 8000;

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon',
    '.svg': 'image/svg+xml',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

const server = http.createServer((req, res) => {
    let filePath = '.' + req.url;

    // Log the request
    console.log(`📥 ${new Date().toLocaleTimeString()} - ${req.method} ${req.url}`);

    // Default to index.html
    if (filePath === './') {
        filePath = './index.html';
    }

    // Get file extension
    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';

    // Read and serve file
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // File not found
                res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>صفحة غير موجودة</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; direction: rtl; }
                            h1 { color: #e74c3c; }
                        </style>
                    </head>
                    <body>
                        <h1>404 - الصفحة غير موجودة</h1>
                        <p>الملف المطلوب غير موجود</p>
                        <a href="/">العودة للصفحة الرئيسية</a>
                    </body>
                    </html>
                `);
            } else {
                // Server error
                res.writeHead(500);
                res.end('خطأ في الخادم: ' + error.code);
            }
        } else {
            // Success
            res.writeHead(200, {
                'Content-Type': mimeType + '; charset=utf-8',
                'Cache-Control': 'no-cache'
            });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(port, () => {
    console.log(`🚀 Serveur démarré sur http://localhost:${port}`);
    console.log(`📧 Application de correspondance arabe disponible`);
    console.log(`⏹️  Appuyez sur Ctrl+C pour arrêter le serveur`);
    console.log(`📁 Répertoire de travail: ${process.cwd()}`);
    console.log(`🔗 Pages disponibles:`);
    console.log(`   - http://localhost:${port}/simple.html (page de test)`);
    console.log(`   - http://localhost:${port}/test.html (test serveur)`);
    console.log(`   - http://localhost:${port}/index.html (page principale)`);
    console.log(`   - http://localhost:${port}/login.html (connexion)`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur...');
    server.close(() => {
        console.log('✅ Serveur arrêté');
        process.exit(0);
    });
});
