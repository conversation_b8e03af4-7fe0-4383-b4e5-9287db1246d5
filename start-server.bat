@echo off
echo 🚀 Démarrage du serveur pour l'application arabe...
echo.

REM Essayer avec Python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python trouvé, démarrage avec Python...
    echo 📧 Application disponible sur: http://localhost:8000
    echo ⏹️  Appuyez sur Ctrl+C pour arrêter
    echo.
    python -m http.server 8000
    goto :end
)

REM Essayer avec Node.js
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js trouvé, démarrage avec Node.js...
    echo 📧 Application disponible sur: http://localhost:8000
    echo ⏹️  Appuyez sur Ctrl+C pour arrêter
    echo.
    node server.js
    goto :end
)

REM Si aucun serveur n'est disponible
echo ❌ Ni Python ni Node.js ne sont disponibles
echo.
echo 💡 Solutions possibles:
echo    1. Installer Python: https://python.org
echo    2. Installer Node.js: https://nodejs.org
echo    3. Ouvrir directement les fichiers HTML dans le navigateur
echo.
echo 🌐 Vous pouvez essayer d'ouvrir directement:
echo    file:///%CD%\simple.html
echo    file:///%CD%\index.html
echo.
pause

:end
echo.
echo 👋 Serveur arrêté
pause
