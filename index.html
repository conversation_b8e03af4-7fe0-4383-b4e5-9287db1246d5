<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المراسلات الإدارية</title>
    <link href="css/bootstrap-arabic.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Main Container -->
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-lg-5 col-md-7 col-sm-9">
                <!-- Activation Card -->
                <div class="card shadow-lg border-0 fade-in">
                    <!-- Header -->
                    <div class="card-header bg-gradient-primary text-white text-center py-4 border-0">
                        <div class="mb-3">
                            <i class="fas fa-envelope-open-text fa-3x"></i>
                        </div>
                        <h2 class="fw-bold mb-2">نظام إدارة المراسلات الإدارية</h2>
                        <p class="mb-0 opacity-75">إدارة المراسلات الواردة والصادرة</p>
                    </div>

                    <!-- License Status Section -->
                    <div class="card-body p-4">
                        <div class="alert alert-warning border-0 mb-4" id="licenseStatus">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-shield-alt fa-2x text-warning"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="alert-heading mb-1">نسخة تجريبية</h5>
                                    <p class="mb-2">نظام إدارة المراسلات الإدارية</p>
                                    <span class="badge bg-warning text-dark">تجريبي</span>
                                </div>
                            </div>
                            
                            <hr class="my-3">
                            
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border-end">
                                        <div class="fw-bold text-warning">حالة الترخيص</div>
                                        <small id="licenseStatusText">نسخة تجريبية نشطة</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border-end">
                                        <div class="fw-bold text-warning">الأيام المتبقية</div>
                                        <small id="daysRemaining">3 أيام</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-warning">تاريخ الانتهاء</div>
                                    <small id="expiryDate">--</small>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" id="progressFill" style="width: 100%"></div>
                                </div>
                                <small class="text-muted mt-1 d-block text-center">فترة التجربة متاحة</small>
                            </div>
                        </div>

                        <!-- Activation Methods -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-key me-2"></i>
                                طرق التفعيل
                            </h5>
                            
                            <!-- Nav Tabs -->
                            <ul class="nav nav-pills nav-fill mb-3" id="activationTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="license-tab" data-bs-toggle="pill" 
                                            data-bs-target="#license" type="button" role="tab">
                                        <i class="fas fa-key me-2"></i>
                                        رمز الترخيص
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="file-tab" data-bs-toggle="pill" 
                                            data-bs-target="#file" type="button" role="tab">
                                        <i class="fas fa-file-alt me-2"></i>
                                        ملف الترخيص
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="online-tab" data-bs-toggle="pill" 
                                            data-bs-target="#online" type="button" role="tab">
                                        <i class="fas fa-globe me-2"></i>
                                        التفعيل عبر الإنترنت
                                    </button>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content" id="activationTabsContent">
                                <!-- License Code Tab -->
                                <div class="tab-pane fade show active" id="license" role="tabpanel">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-key me-2"></i>
                                            مفتاح الترخيص
                                        </label>
                                        <div class="row g-2">
                                            <div class="col-3">
                                                <input type="text" id="licenseKey1" class="form-control text-center" 
                                                       maxlength="4" placeholder="XXXX">
                                            </div>
                                            <div class="col-3">
                                                <input type="text" id="licenseKey2" class="form-control text-center" 
                                                       maxlength="4" placeholder="XXXX">
                                            </div>
                                            <div class="col-3">
                                                <input type="text" id="licenseKey3" class="form-control text-center" 
                                                       maxlength="4" placeholder="XXXX">
                                            </div>
                                            <div class="col-3">
                                                <input type="text" id="licenseKey4" class="form-control text-center" 
                                                       maxlength="4" placeholder="XXXX">
                                            </div>
                                        </div>
                                        <div class="form-text">أدخل مفتاح الترخيص المكون من 16 رقم/حرف</div>
                                    </div>
                                    <button type="button" class="btn btn-primary w-100" onclick="activateWithKey()">
                                        <i class="fas fa-rocket me-2"></i>
                                        تفعيل الترخيص
                                    </button>
                                </div>

                                <!-- License File Tab -->
                                <div class="tab-pane fade" id="file" role="tabpanel">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-file-alt me-2"></i>
                                            ملف الترخيص
                                        </label>
                                        <div class="border border-2 border-dashed rounded p-4 text-center" 
                                             style="cursor: pointer;" onclick="document.getElementById('licenseFile').click()">
                                            <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                            <p class="mb-0">اسحب وأفلت ملف الترخيص هنا</p>
                                            <small class="text-muted">أو انقر للاختيار</small>
                                            <input type="file" id="licenseFile" accept=".lic,.key,.license" 
                                                   style="display: none;" onchange="handleLicenseFile(this)">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-success w-100" onclick="activateWithFile()" 
                                            disabled id="fileActivateBtn">
                                        <i class="fas fa-file-check me-2"></i>
                                        تفعيل بملف الترخيص
                                    </button>
                                </div>

                                <!-- Online Activation Tab -->
                                <div class="tab-pane fade" id="online" role="tabpanel">
                                    <div class="mb-3">
                                        <div class="row g-3 mb-3">
                                            <div class="col-4 text-center">
                                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <span class="fw-bold">1</span>
                                                </div>
                                                <small class="d-block mt-2">أدخل بريدك الإلكتروني</small>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="bg-secondary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <span class="fw-bold">2</span>
                                                </div>
                                                <small class="d-block mt-2">استلام رمز التفعيل</small>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <span class="fw-bold">3</span>
                                                </div>
                                                <small class="d-block mt-2">إكمال التفعيل</small>
                                            </div>
                                        </div>
                                        
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-envelope me-2"></i>
                                            البريد الإلكتروني
                                        </label>
                                        <input type="email" id="userEmail" class="form-control" 
                                               placeholder="<EMAIL>">
                                    </div>
                                    <button type="button" class="btn btn-info w-100" onclick="requestOnlineActivation()">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        طلب رمز التفعيل
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Trial Access -->
                        <div class="text-center">
                            <div class="alert alert-info border-0 mb-3">
                                <div class="d-flex align-items-center justify-content-center">
                                    <i class="fas fa-clock me-2"></i>
                                    <div>
                                        <strong>متابعة بالنسخة التجريبية</strong>
                                        <br>
                                        <small>يمكنك استخدام النظام لمدة 3 أيام مجاناً</small>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary w-100" onclick="continueWithTrial()">
                                <i class="fas fa-play me-2"></i>
                                متابعة التجربة المجانية
                            </button>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="card-footer bg-light text-center border-0">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            نظام إدارة المراسلات الواردة والصادرة
                        </small>
                        <div id="securityInfo" class="mt-2" style="display: none;">
                            <small class="text-success">
                                <i class="fas fa-shield-alt me-1"></i>
                                نظام حماية متقدم ضد التلاعب
                            </small>
                        </div>
                        <div id="devModeInfo" class="mt-2" style="display: none;">
                            <small class="text-warning">
                                <i class="fas fa-code me-1"></i>
                                وضع التطوير: افتح وحدة تحكم المطور (F12) لرؤية معلومات إضافية
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Application Scripts -->
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>

    <script>
        // Initialize activation system on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent multiple initializations
            if (window.activationInitialized) return;
            window.activationInitialized = true;

            initializeActivationSystem();

            // Show development mode info if applicable
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                const devInfo = document.getElementById('devModeInfo');
                if (devInfo) devInfo.style.display = 'block';
            }

            // Show security info after a delay
            setTimeout(() => {
                const secInfo = document.getElementById('securityInfo');
                if (secInfo) secInfo.style.display = 'block';
            }, 2000);

            // Auto-move to next input field
            setupLicenseKeyInputs();
        });

        // Setup license key inputs auto-navigation
        function setupLicenseKeyInputs() {
            const inputs = ['licenseKey1', 'licenseKey2', 'licenseKey3', 'licenseKey4'];
            
            inputs.forEach((inputId, index) => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', function() {
                        if (this.value.length === 4 && index < inputs.length - 1) {
                            document.getElementById(inputs[index + 1]).focus();
                        }
                    });
                    
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Backspace' && this.value.length === 0 && index > 0) {
                            document.getElementById(inputs[index - 1]).focus();
                        }
                    });
                }
            });
        }

        // Tab switching functions for compatibility
        function switchTab(tabName) {
            const tabMap = {
                'license': 'license-tab',
                'file': 'file-tab',
                'online': 'online-tab'
            };
            
            const tabButton = document.getElementById(tabMap[tabName]);
            if (tabButton) {
                const tab = new bootstrap.Tab(tabButton);
                tab.show();
            }
        }
    </script>
</body>
</html>
